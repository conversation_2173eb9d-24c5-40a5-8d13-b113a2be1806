package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/joho/godotenv"
	"github.com/tmc/langchaingo/llms/googleai"
)

func main() {
	SimplePrompt()
}

func SimplePrompt() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Fatal("Error loading .env file", err)
	}

	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		log.Fatal("GEMINI_API_KEY environment variable is not set")
	}

	var ctx, cancel = context.WithTimeout(context.Background(), 100*time.Second)
	defer cancel()

	llm, err := googleai.New(ctx,
		googleai.WithAPIKey(apiKey),
		googleai.WithDefaultModel("gemini-2.5-flash"),
	)

	if err != nil {
		log.Fatalf("Error creating LLM: %v", err)
	}

	prompt := "What is educative?"

	response, err := llm.Call(ctx, prompt)

	if err != nil {
		log.Fatalf("Error calling LLM (prompt: %q): %v", prompt, err)
	}

	fmt.Println(response)
}
